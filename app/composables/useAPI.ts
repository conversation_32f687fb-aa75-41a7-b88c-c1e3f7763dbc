import axios from 'axios'

let isRefreshingToken = false
let requestsPending = []

// Cache to track last system maintenance check time (to limit to once per minute)
let lastSystemMaintenanceCheck = 0
const MAINTENANCE_CHECK_INTERVAL = 60 * 1000 // 1 minute in milliseconds

// Helper function to reset system maintenance check cache (useful for testing)
const resetSystemMaintenanceCheckCache = () => {
  lastSystemMaintenanceCheck = 0
}

// Export for testing purposes
if (typeof window !== 'undefined') {
  (window as any).resetSystemMaintenanceCheckCache = resetSystemMaintenanceCheckCache
}

// Helper function to check maintenance after successful API calls
const checkSystemMaintenanceAfterApiCall = async (config: any) => {
  try {
    const authStore = useAuthStore()

    // Only call checkSystemMaintenance if:
    // 1. User is not a logged-in Operator (allow non-authenticated users and non-operators)
    // 2. The request is not to maintenance endpoints (to avoid infinite loops)
    const requestUrl = config.config?.url || ''
    const baseURL = config.config?.baseURL || ''

    // Get runtime config to check maintenance service URL
    const runtimeConfig = useRuntimeConfig()
    const maintenanceServiceBaseUrl = runtimeConfig.public.api.maintenanceServiceBaseUrl

    // Check if this is a maintenance service call to avoid infinite loops
    const isMaintenanceServiceCall = baseURL === maintenanceServiceBaseUrl
      && requestUrl.startsWith('/tenants/')

    // Skip maintenance check only for logged-in operators
    const shouldSkipMaintenanceCheck = authStore.user && authStore.isOperator

    if (
      !shouldSkipMaintenanceCheck
      && requestUrl
      && !isMaintenanceServiceCall // Avoid calling on maintenance service endpoints
      && !requestUrl.includes('/login') // Avoid calling on auth endpoints
      && !requestUrl.includes('/logout')
      && !requestUrl.includes('/refresh')
      && !requestUrl.includes('/operator/login') // Avoid calling on operator login endpoints
    ) {
      const now = Date.now()

      // Check if we've already called checkSystemMaintenance within the last minute
      if (lastSystemMaintenanceCheck && (now - lastSystemMaintenanceCheck) < MAINTENANCE_CHECK_INTERVAL) {
        // Skip this call - we've already checked within the last minute
        return
      }

      // Update the cache with current timestamp
      lastSystemMaintenanceCheck = now

      // Import and call checkSystemMaintenance
      const { useMaintenanceStore } = await import('~/stores/maintenance')
      const maintenanceStore = useMaintenanceStore()

      // Call checkSystemMaintenance in the background (don't await to avoid blocking the response)
      maintenanceStore.checkSystemMaintenance().catch((error) => {
        console.warn('Background checkSystemMaintenance failed:', error)
        // Reset cache on error so we can retry sooner
        lastSystemMaintenanceCheck = 0
      })
    }
  } catch (error) {
    // Don't let maintenance check errors affect the original API response
    console.warn('Error in system maintenance check interceptor:', error)
  }
}

const createAxios = (baseURL) => {
  const { handleApiError } = useApiErrorHandler()
  const newInstance = axios.create({ baseURL })
  newInstance.interceptors.request.use((config) => {
    const authStore = useAuthStore()
    const accessToken = authStore.token
    const refreshToken = authStore.refresh_token
    if (['/login/refresh'].includes(config.url) && refreshToken) {
      config.headers['Authorization'] = 'Bearer ' + refreshToken
    } else if (accessToken) {
      config.headers['Authorization'] = 'Bearer ' + accessToken
    }
    return config
  })

  /**
    * @description if any of the API gets 401 status code, this method
    calls getAuthToken method to renew accessToken
    * updates the error configuration and retries all failed requests
    again
  */
  newInstance.interceptors.response.use(
    (config) => {
      // Call checkSystemMaintenance after successful API calls for non-Operator users (non-blocking)
      checkSystemMaintenanceAfterApiCall(config)

      return { ...config, body: config.data }
    },
    async (error) => {
      const _errorResponse = error.response
      const originalRequest = error.config
      const status = _errorResponse.status
      const authStore = useAuthStore()
      // check it is login/refresh request, logout
      if (['/login/refresh'].includes(originalRequest.url)) {
        await authStore.logout()
        return Promise.reject(error)
      }
      // const { authService } = _useAPI()
      switch (status) {
        case 401:
          if (['/logout'].includes(originalRequest.url)) {
            // if auth_code request fails, redirect to login page
            window.location.href = '/login?redirect=/mvno-mos/'
          } else if (['/login/refresh'].includes(originalRequest.url)) {
            await authStore.logout()
            return Promise.reject(error)
          } else {
            // retry with refrest token first
            if (!originalRequest._retry) {
              // save request if it is not refresh token request
              if (isRefreshingToken) {
                return new Promise(function (resolve) {
                  requestsPending.push(function () {
                    resolve(newInstance(originalRequest))
                  })
                })
              }
              originalRequest._retry = true
              isRefreshingToken = true

              try {
                const refreshTokenRes = await authStore.refreshToken()

                if (refreshTokenRes) {
                  originalRequest.headers['Authorization']
                    = 'Bearer ' + refreshTokenRes.token
                  return newInstance(originalRequest)
                } else {
                  await authStore.logout()
                }
              } catch (refreshError) {
                isRefreshingToken = false
                await authStore.logout()
                return Promise.reject(refreshError)
              } finally {
                isRefreshingToken = false
                requestsPending.forEach(callback => callback())
                requestsPending = []
              }
            } else {
              await authStore.logout()
            }
          }

          authStore.logout()
          break
        case 404:
          // Use i18n error handler for 404 errors
          handleApiError(error, {
            ignoredStatuses: [], // Don't ignore 404 for admin service
            timeout: 30000
          })
          break

        default:
          // Use i18n error handler for all other errors
          handleApiError(error, {
            timeout: 30000
          })
          break
      }

      return Promise.reject(error)
    }
  )

  return newInstance
}

const createRagAxios = (baseURL: string) => {
  const { selectedTenantId } = useApp()
  const { handleApiError } = useApiErrorHandler()
  const newInstance = axios.create({ baseURL })
  newInstance.interceptors.request.use((config) => {
    const ragsStore = useRagsStore()
    const accessToken = ragsStore.tenantTokens[selectedTenantId.value]?.token
    const refreshToken
      = ragsStore.tenantTokens[selectedTenantId.value]?.refresh_token
    if (['/login/refresh'].includes(config.url) && refreshToken) {
      config.headers['Authorization'] = 'Bearer ' + refreshToken
    } else if (accessToken) {
      config.headers['Authorization'] = 'Bearer ' + accessToken
    }
    return config
  })

  /**
    * @description if any of the API gets 401 status code, this method
    calls getAuthToken method to renew accessToken
    * updates the error configuration and retries all failed requests
    again
  */
  newInstance.interceptors.response.use(
    (config) => {
      // Call checkSystemMaintenance after successful API calls for non-Operator users (non-blocking)
      checkSystemMaintenanceAfterApiCall(config)

      return { ...config, body: config.data }
    },
    async (error) => {
      const _errorResponse = error.response
      const originalRequest = error.config
      const status = _errorResponse.status
      const ragsStore = useRagsStore()
      switch (status) {
        case 401:
          if (['/login/refresh'].includes(originalRequest.url)) {
            return Promise.reject(error)
          } else {
            // retry with refrest token first
            if (!originalRequest._retry) {
              // save request if it is not refresh token request
              if (isRefreshingToken) {
                return new Promise(function (resolve) {
                  requestsPending.push(function () {
                    resolve(newInstance(originalRequest))
                  })
                })
              }
              originalRequest._retry = true
              isRefreshingToken = true

              try {
                const refreshTokenRes = await ragsStore.refreshToken()

                if (refreshTokenRes) {
                  originalRequest.headers['Authorization']
                    = 'Bearer ' + refreshTokenRes.token
                  return newInstance(originalRequest)
                } else {
                  await ragsStore.logout()
                }
              } catch (refreshError) {
                isRefreshingToken = false
                await ragsStore.logout()
                return Promise.reject(refreshError)
              } finally {
                isRefreshingToken = false
                requestsPending.forEach(callback => callback())
                requestsPending = []
              }
            } else {
              await ragsStore.logout()
            }
          }

          ragsStore.logout()
          break
        default:
          // Use i18n error handler for RAG service errors
          handleApiError(error, {
            customTitle: 'RAG Service エラー'
          })
          break
      }
      return Promise.reject(error)
    }
  )

  return newInstance
}

const createReportAxios = (baseURL: string) => {
  const { selectedTenantId } = useApp()
  const { handleApiError } = useApiErrorHandler()
  const newInstance = axios.create({ baseURL })
  newInstance.interceptors.request.use((config: any) => {
    const ragsStore = useRagsStore()
    const accessToken = ragsStore.tenantTokens[selectedTenantId.value]?.token
    const refreshToken
      = ragsStore.tenantTokens[selectedTenantId.value]?.refresh_token
    if (['/login/refresh'].includes(config.url) && refreshToken) {
      config.headers['Authorization'] = 'Bearer ' + refreshToken
    } else if (accessToken) {
      config.headers['Authorization'] = 'Bearer ' + accessToken
    }
    return config
  })

  /**
    * @description if any of the API gets 401 status code, this method
    calls getAuthToken method to renew accessToken
    * updates the error configuration and retries all failed requests
    again
  */
  newInstance.interceptors.response.use(
    (config) => {
      // Call checkSystemMaintenance after successful API calls for non-Operator users (non-blocking)
      checkSystemMaintenanceAfterApiCall(config)

      return { ...config, body: config.data }
    },
    async (error) => {
      const _errorResponse = error.response
      const originalRequest = error.config
      const status = _errorResponse.status
      const ragsStore = useRagsStore()
      switch (status) {
        case 401:
          if (['/login/refresh'].includes(originalRequest.url)) {
            return Promise.reject(error)
          } else {
            // retry with refrest token first
            if (!originalRequest._retry) {
              // save request if it is not refresh token request
              if (isRefreshingToken) {
                return new Promise(function (resolve) {
                  requestsPending.push(function () {
                    resolve(newInstance(originalRequest))
                  })
                })
              }
              originalRequest._retry = true
              isRefreshingToken = true

              try {
                const refreshTokenRes = await ragsStore.refreshToken()

                if (refreshTokenRes) {
                  originalRequest.headers['Authorization']
                    = 'Bearer ' + refreshTokenRes.token
                  return newInstance(originalRequest)
                } else {
                  await ragsStore.logout()
                }
              } catch (refreshError) {
                isRefreshingToken = false
                await ragsStore.logout()
                return Promise.reject(refreshError)
              } finally {
                isRefreshingToken = false
                requestsPending.forEach(callback => callback())
                requestsPending = []
              }
            } else {
              await ragsStore.logout()
            }
          }

          ragsStore.logout()
          break
        default:
          // Use i18n error handler for Report service errors
          handleApiError(error, {
            customTitle: 'Report Service エラー'
          })
          break
      }
      return Promise.reject(error)
    }
  )

  return newInstance
}

const _useAPI = () => {
  const runtimeConfig = useRuntimeConfig()
  const adminService = createAxios(
    runtimeConfig.public.api.adminServiceBaseUrl
  )
  const authService = createAxios(runtimeConfig.public.api.authServiceBaseUrl)
  const ragService = createRagAxios(runtimeConfig.public.api.ragServiceBaseUrl)

  const reportService = createReportAxios(
    runtimeConfig.public.api.reportServiceBaseUrl
  )

  const maintenanceService = createAxios(
    runtimeConfig.public.api.maintenanceServiceBaseUrl
  )

  return {
    adminService,
    authService,
    ragService,
    reportService,
    maintenanceService
  }
}

export const useAPI = _useAPI
