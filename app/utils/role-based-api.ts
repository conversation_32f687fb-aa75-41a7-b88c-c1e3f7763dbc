import { UserType } from '~/types/index.d'

/**
 * Interface for role-based API endpoints
 */
export interface RoleBasedEndpoints<T = any> {
  /**
   * Endpoint for PNL_ADMIN/Operator role
   */
  operator: string

  /**
   * Endpoint for ADMIN role
   */
  admin: string

  /**
   * Endpoint for STAFF role
   */
  staff: string

  /**
   * Endpoint for GUEST role
   */
  guest?: string

  /**
   * Default endpoint if no role matches
   */
  default: string

  /**
   * Optional parameters for the endpoint
   * These will be replaced in the endpoint string using the format {paramName}
   */
  params?: Record<string, any>

  /**
   * Optional query parameters for the endpoint
   */
  query?: Record<string, any>

  /**
   * Optional transformer function to process the response data
   */
  transform?: (data: any) => T
}

/**
 * Get the appropriate API endpoint based on user role
 * @param endpoints Object containing endpoints for different roles
 * @param userRole Current user role
 * @returns The appropriate endpoint for the user role with parameters replaced
 */
export function getRoleBasedEndpoint(
  endpoints: RoleBasedEndpoints,
  userRole: UserType | null
): string {
  // Determine which endpoint to use based on role
  let endpoint: string

  switch (userRole) {
    case UserType.PNL_ADMIN:
      endpoint = endpoints.operator
      break
    case UserType.ADMIN:
      endpoint = endpoints.admin
      break
    case UserType.STAFF:
      endpoint = endpoints.staff
      break
    case UserType.GUEST:
      endpoint = endpoints.guest || endpoints.default
      break
    default:
      endpoint = endpoints.default
  }

  // Replace parameters in the endpoint string
  if (endpoints.params) {
    // Check if any required parameters are undefined or null
    const missingParams = Object.entries(endpoints.params)
      .filter(([key, value]) => {
        // Check if the parameter is in the endpoint and its value is undefined or null
        return (
          endpoint.includes(`{${key}}`)
          && (value === undefined || value === null)
        )
      })
      .map(([key]) => key)

    // If there are missing required parameters, use the operator endpoint as fallback
    // This is useful for first login when tenantId might be undefined
    if (missingParams.length > 0 && endpoints.operator) {
      console.warn(
        `Missing required parameters: ${missingParams.join(
          ', '
        )}. Using operator endpoint as fallback.`
      )
      endpoint = endpoints.operator
    }

    // Replace parameters in the endpoint string
    Object.entries(endpoints.params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        endpoint = endpoint.replace(`{${key}}`, value)
      }
    })
  }

  // Add query parameters if they exist
  if (endpoints.query) {
    const queryParams = new URLSearchParams()
    Object.entries(endpoints.query).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString())
      }
    })

    const queryString = queryParams.toString()
    if (queryString) {
      endpoint += (endpoint.includes('?') ? '&' : '?') + queryString
    }
  }

  return endpoint
}

/**
 * Make a role-based API call
 * @param endpoints Object containing endpoints for different roles
 * @param userRole Current user role
 * @param apiService API service to use for the call
 * @param method HTTP method to use
 * @param data Optional data to send with the request
 * @returns Promise with the response data
 */
export async function makeRoleBasedApiCall<T = any>(
  endpoints: RoleBasedEndpoints<T>,
  userRole: UserType | null,
  apiService: any,
  method: 'get' | 'post' | 'put' | 'patch' | 'delete' = 'get',
  data?: any
): Promise<T> {
  const endpoint = getRoleBasedEndpoint(endpoints, userRole)

  let response
  switch (method) {
    case 'post':
      response = await apiService.post(endpoint, data)
      break
    case 'put':
      response = await apiService.put(endpoint, data)
      break
    case 'patch':
      response = await apiService.patch(endpoint, data)
      break
    case 'delete':
      response = await apiService.delete(endpoint)
      break
    case 'get':
    default:
      response = await apiService.get(endpoint)
  }

  // Transform the response data if a transformer function is provided
  if (endpoints.transform && response.data) {
    return endpoints.transform(response.data)
  }
  if (response.headers['x-pagination']) {
    response.data.pagination = response.headers['x-pagination']
  }

  return response.data
}

/**
 * Create a composable for role-based API calls
 */
export function useRoleBasedApi() {
  const authStore = useAuthStore()
  const { userRole } = storeToRefs(authStore)
  const api = useAPI()

  /**
   * Make a role-based API call
   * @param endpoints Object containing endpoints for different roles
   * @param method HTTP method to use
   * @param data Optional data to send with the request
   * @param service API service to use (defaults to adminService)
   * @returns Promise with the response data
   */
  async function call<T = any>(
    endpoints: RoleBasedEndpoints<T>,
    method: 'get' | 'post' | 'put' | 'patch' | 'delete' = 'get',
    data?: any,
    service:
      | 'adminService'
      | 'authService'
      | 'ragService'
      | 'reportService'
      | 'maintenanceService' = 'adminService'
  ): Promise<T> {
    return makeRoleBasedApiCall<T>(
      endpoints,
      userRole.value,
      api[service],
      method,
      data
    )
  }

  return {
    call
  }
}
