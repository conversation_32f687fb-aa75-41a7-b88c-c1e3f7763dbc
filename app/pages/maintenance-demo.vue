<script setup lang="ts">
// Use blank layout
definePageMeta({
  layout: 'blank'
})

// Set page title
useHead({
  title: 'Maintenance Demo - PNL'
})

// Demo data for testing
const demoData = ref({
  message: 'システムメンテナンス中です。サーバーのアップグレード作業を実施しております。ご不便をおかけして申し訳ございません。',
  startDate: new Date().toISOString(),
  endDate: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours from now
  showCountdown: true
})

// Demo controls
const demoControls = ref({
  customMessage: demoData.value.message,
  hoursFromNow: 2,
  showCountdown: true
})

// Update demo data
const updateDemoData = () => {
  demoData.value = {
    message: demoControls.value.customMessage,
    startDate: new Date().toISOString(),
    endDate: new Date(Date.now() + demoControls.value.hoursFromNow * 60 * 60 * 1000).toISOString(),
    showCountdown: demoControls.value.showCountdown
  }
}

// Preset messages
const presetMessages = [
  'システムメンテナンス中です。しばらくお待ちください。',
  'システムメンテナンス中です。サーバーのアップグレード作業を実施しております。ご不便をおかけして申し訳ございません。',
  'システムメンテナンス中です。データベースの最適化作業を行っております。',
  'システムメンテナンス中です。セキュリティアップデートを適用しております。',
  '緊急メンテナンス中です。システムの不具合修正を行っております。'
]

const showDemo = ref(false)
</script>

<template>
  <div>
    <!-- Demo Controls (only show if not in demo mode) -->
    <div
      v-if="!showDemo"
      class="demo-controls"
    >
      <div class="controls-container">
        <h2 class="text-2xl font-bold text-gray-800 dark:text-gray-100 mb-6">
          Maintenance Display Demo
        </h2>

        <div class="control-group">
          <label class="control-label">メッセージ:</label>
          <UTextarea
            v-model="demoControls.customMessage"
            :rows="3"
            placeholder="カスタムメッセージを入力..."
          />

          <div class="preset-buttons">
            <UButton
              v-for="(preset, index) in presetMessages"
              :key="index"
              variant="outline"
              size="sm"
              @click="demoControls.customMessage = preset"
            >
              プリセット {{ index + 1 }}
            </UButton>
          </div>
        </div>

        <div class="control-group">
          <label class="control-label">終了時刻 (現在から何時間後):</label>
          <UInput
            v-model.number="demoControls.hoursFromNow"
            type="number"
            :min="0"
            :max="24"
            :step="0.5"
          />
        </div>

        <div class="control-group">
          <UCheckbox
            v-model="demoControls.showCountdown"
            label="カウントダウンを表示"
          />
        </div>

        <div class="control-actions">
          <UButton
            color="primary"
            size="lg"
            @click="updateDemoData(); showDemo = true"
          >
            デモを開始
          </UButton>
        </div>
      </div>
    </div>

    <!-- Maintenance Display -->
    <div v-if="showDemo">
      <MaintenanceDisplay
        :message="demoData.message"
        :start-date="demoData.startDate"
        :end-date="demoData.endDate"
        :show-countdown="demoData.showCountdown"
      />

      <!-- Back to controls button -->
      <div class="demo-back-button">
        <UButton
          variant="outline"
          color="gray"
          @click="showDemo = false"
        >
          設定に戻る
        </UButton>
      </div>
    </div>
  </div>
</template>

<style scoped>
.demo-controls {
  @apply min-h-screen flex items-center justify-center p-8;
}

.controls-container {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 max-w-2xl w-full border border-gray-200 dark:border-gray-700;
}

.control-group {
  @apply mb-6;
}

.control-label {
  @apply block text-sm font-medium text-gray-800 dark:text-gray-300 mb-2;
}

.preset-buttons {
  @apply flex flex-wrap gap-2 mt-3;
}

.control-actions {
  @apply flex justify-center pt-4;
}

.demo-back-button {
  @apply fixed bottom-8 right-8;
}
</style>
