<script setup lang="ts">
definePageMeta({
  middleware: ['authentication', 'role-guard']
})

const authStore = useAuthStore()
const tenantsStore = useTenantsStore()
const { selectedTenant } = storeToRefs(tenantsStore)
// Check user permissions
const canViewMaintenance = computed(() => {
  return authStore.isAdmin || authStore.isOperator
})

const canManageAll = computed(() => {
  return authStore.isOperator
})

// Page title and description
const pageTitle = 'メンテナンス設定'
const pageDescription = 'チャットボットのメンテナンス設定できます'
</script>

<template>
  <UDashboardPanelContent class="pb-24">
    <!-- Page Header -->
    <UDashboardSection
      :title="pageTitle"
      :description="pageDescription"
    />

    <!-- Permission Check -->
    <UAlert
      v-if="!canViewMaintenance"
      color="red"
      variant="soft"
      title="アクセス権限がありません"
      description="このページを表示するには管理者以上の権限が必要です。"
      class="mb-6"
    />

    <!-- Content for authorized users -->
    <div
      v-else
      class="space-y-8"
    >
      <div class="space-y-6">
        <MaintenanceControl
          :tenant="selectedTenant"
          :show-all-controls="canManageAll"
        />
      </div>
    </div>
  </UDashboardPanelContent>
</template>
