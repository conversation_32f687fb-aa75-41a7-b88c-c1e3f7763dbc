<script setup lang="ts">
definePageMeta({
  middleware: ['authentication', 'role-guard']
})

// Demo countdown end dates
const demoEndDates = ref([
  {
    label: '30秒後',
    endDate: new Date(Date.now() + 30 * 1000).toISOString(),
    variant: 'danger' as const
  },
  {
    label: '5分後',
    endDate: new Date(Date.now() + 5 * 60 * 1000).toISOString(),
    variant: 'warning' as const
  },
  {
    label: '1時間後',
    endDate: new Date(Date.now() + 60 * 60 * 1000).toISOString(),
    variant: 'default' as const
  },
  {
    label: '1日後',
    endDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    variant: 'default' as const
  }
])

// Custom countdown
const customEndDate = ref('')
const customMessage = ref('カスタムカウントダウン')

// Helper function to get current Japan time for datetime-local input
const toJapanLocalDateTime = (date: Date): string => {
  // Convert to Japan timezone (UTC+9) and format for datetime-local input
  const japanTime = new Date(date.toLocaleString("en-US", {timeZone: "Asia/Tokyo"}))

  // Format for datetime-local input (YYYY-MM-DDTHH:mm)
  const year = japanTime.getFullYear()
  const month = String(japanTime.getMonth() + 1).padStart(2, '0')
  const day = String(japanTime.getDate()).padStart(2, '0')
  const hours = String(japanTime.getHours()).padStart(2, '0')
  const minutes = String(japanTime.getMinutes()).padStart(2, '0')

  return `${year}-${month}-${day}T${hours}:${minutes}`
}

// Initialize custom end date to 1 hour from now in Japan time
onMounted(() => {
  const oneHourLater = new Date(Date.now() + 60 * 60 * 1000)
  customEndDate.value = toJapanLocalDateTime(oneHourLater) // Japan local time for datetime-local input
})

// Handle countdown events
const handleCountdownExpired = (label: string) => {
  const toast = useToast()
  toast.add({
    title: 'カウントダウン終了',
    description: `${label}のカウントダウンが終了しました`,
    color: 'green'
  })
}

const handleTimeUpdate = (label: string, timeRemaining: any) => {
  console.log(`${label} - Time remaining:`, timeRemaining)
}

// Format datetime for API
const formatDateTime = (date: Date): string => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// Get custom end date in API format
const customEndDateFormatted = computed(() => {
  if (!customEndDate.value) return ''
  // Send Japan time directly (no UTC conversion)
  return formatDateTime(new Date(customEndDate.value))
})
</script>

<template>
  <UDashboardPanelContent class="pb-24">
    <!-- Page Header -->
    <UDashboardSection
      title="メンテナンスカウントダウンデモ"
      description="MaintenanceCountdownコンポーネントのデモページです。"
    >
      <template #links>
        <UButton
          to="/settings"
          color="gray"
          variant="ghost"
          size="sm"
          icon="i-heroicons-arrow-left"
        >
          設定に戻る
        </UButton>
      </template>
    </UDashboardSection>

    <div class="space-y-8">
      <!-- Demo Countdowns -->
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">
            プリセットカウントダウン
          </h3>
        </template>

        <div class="space-y-6">
          <div
            v-for="demo in demoEndDates"
            :key="demo.label"
            class="flex items-center justify-between p-4 border rounded-lg"
          >
            <div>
              <h4 class="font-medium">{{ demo.label }}</h4>
              <p class="text-sm text-gray-500">
                終了時刻: {{ new Date(demo.endDate).toLocaleString('ja-JP') }}
              </p>
            </div>
            <MaintenanceCountdown
              :end-date="demo.endDate"
              :variant="demo.variant"
              size="md"
              @expired="() => handleCountdownExpired(demo.label)"
              @time-update="(time) => handleTimeUpdate(demo.label, time)"
            />
          </div>
        </div>
      </UCard>

      <!-- Custom Countdown -->
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">
            カスタムカウントダウン
          </h3>
        </template>

        <div class="space-y-6">
          <!-- Configuration Form -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UFormGroup
              label="終了日時"
              description="カウントダウンの終了日時を設定（日本時間 JST/UTC+9）"
            >
              <UInput
                v-model="customEndDate"
                type="datetime-local"
              />
              <template #help>
                <div class="flex items-center gap-1 text-xs text-blue-600 mt-1">
                  <UIcon name="i-heroicons-information-circle" class="w-3 h-3" />
                  <span>日本標準時（JST）で入力してください</span>
                </div>
              </template>
            </UFormGroup>

            <UFormGroup
              label="メッセージ"
              description="カウントダウンに表示するメッセージ"
            >
              <UInput
                v-model="customMessage"
                placeholder="カスタムカウントダウン"
              />
            </UFormGroup>
          </div>

          <!-- Custom Countdown Display -->
          <div class="p-4 bg-gray-50 rounded-lg">
            <h4 class="font-medium mb-3">カスタムカウントダウン表示</h4>
            <div class="space-y-3">
              <!-- Different sizes -->
              <div class="flex items-center gap-4">
                <span class="w-16 text-sm text-gray-600">Small:</span>
                <MaintenanceCountdown
                  v-if="customEndDateFormatted"
                  :end-date="customEndDateFormatted"
                  :message="customMessage"
                  size="sm"
                  variant="default"
                />
              </div>

              <div class="flex items-center gap-4">
                <span class="w-16 text-sm text-gray-600">Medium:</span>
                <MaintenanceCountdown
                  v-if="customEndDateFormatted"
                  :end-date="customEndDateFormatted"
                  :message="customMessage"
                  size="md"
                  variant="warning"
                />
              </div>

              <div class="flex items-center gap-4">
                <span class="w-16 text-sm text-gray-600">Large:</span>
                <MaintenanceCountdown
                  v-if="customEndDateFormatted"
                  :end-date="customEndDateFormatted"
                  :message="customMessage"
                  size="lg"
                  variant="danger"
                />
              </div>
            </div>
          </div>
        </div>
      </UCard>

      <!-- Component Usage -->
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">
            使用方法
          </h3>
        </template>

        <div class="space-y-4">
          <div>
            <h4 class="font-medium mb-2">基本的な使用例</h4>
            <pre class="bg-gray-100 p-4 rounded text-sm overflow-auto"><code>&lt;MaintenanceCountdown
  end-date="2024-12-31 23:59:59"
  message="メンテナンス終了まで"
  size="md"
  variant="warning"
  @expired="handleExpired"
  @time-update="handleTimeUpdate"
/&gt;</code></pre>
          </div>

          <div>
            <h4 class="font-medium mb-2">プロパティ</h4>
            <div class="overflow-x-auto">
              <table class="min-w-full text-sm">
                <thead>
                  <tr class="border-b">
                    <th class="text-left p-2">プロパティ</th>
                    <th class="text-left p-2">型</th>
                    <th class="text-left p-2">デフォルト</th>
                    <th class="text-left p-2">説明</th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="border-b">
                    <td class="p-2 font-mono">end-date</td>
                    <td class="p-2">string</td>
                    <td class="p-2">-</td>
                    <td class="p-2">終了日時 (YYYY-MM-DD HH:mm:ss または ISO形式)</td>
                  </tr>
                  <tr class="border-b">
                    <td class="p-2 font-mono">message</td>
                    <td class="p-2">string</td>
                    <td class="p-2">'メンテナンス終了まで'</td>
                    <td class="p-2">表示メッセージ</td>
                  </tr>
                  <tr class="border-b">
                    <td class="p-2 font-mono">size</td>
                    <td class="p-2">'sm' | 'md' | 'lg'</td>
                    <td class="p-2">'md'</td>
                    <td class="p-2">サイズ</td>
                  </tr>
                  <tr class="border-b">
                    <td class="p-2 font-mono">variant</td>
                    <td class="p-2">'default' | 'warning' | 'danger'</td>
                    <td class="p-2">'default'</td>
                    <td class="p-2">色のバリエーション</td>
                  </tr>
                  <tr class="border-b">
                    <td class="p-2 font-mono">show-icon</td>
                    <td class="p-2">boolean</td>
                    <td class="p-2">true</td>
                    <td class="p-2">アイコンの表示</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <div>
            <h4 class="font-medium mb-2">イベント</h4>
            <div class="space-y-2">
              <div class="p-3 bg-gray-50 rounded">
                <code class="font-mono">@expired</code> - カウントダウンが終了した時に発火
              </div>
              <div class="p-3 bg-gray-50 rounded">
                <code class="font-mono">@time-update</code> - 毎秒時間が更新される時に発火
              </div>
            </div>
          </div>
        </div>
      </UCard>
    </div>
  </UDashboardPanelContent>
</template>
