<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold mb-6">Test TenantsSelect Component</h1>
    
    <div class="max-w-md">
      <h2 class="text-lg font-semibold mb-4">Tenant Selection:</h2>
      <TenantsSelect />
    </div>

    <div class="mt-8">
      <h2 class="text-lg font-semibold mb-4">Current State:</h2>
      <div class="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
        <pre>{{ JSON.stringify(tenantsStore, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const tenantsStore = useTenantsStore()

// Fetch tenants on mount for testing
onMounted(() => {
  tenantsStore.fetchTenants()
})
</script>
