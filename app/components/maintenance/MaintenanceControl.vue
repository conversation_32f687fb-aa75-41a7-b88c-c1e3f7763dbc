<script setup lang="ts">
import type {
  MaintenanceStatus,
  MaintenanceActivationPayload
} from '~/types/index.d'

interface Props {
  tenant: any
  showAllControls?: boolean // Show operator-only controls
}

const props = withDefaults(defineProps<Props>(), {
  showAllControls: false
})

const maintenanceStore = useMaintenanceStore()
const authStore = useAuthStore()
const confirm = useConfirm()

const { allActiveMaintenanceStatus, loadings } = storeToRefs(maintenanceStore)

// Filter to only show currently active or future maintenance
const filteredActiveMaintenanceStatus = computed(() => {
  return allActiveMaintenanceStatus.value.filter((status) => {
    // Only show if maintenance flag is true
    if (!status.maintenance) return false

    // For indefinite maintenance (no end_date), always show if maintenance is true
    if (!status.end_date) return true

    // For scheduled maintenance, only show if end_date is in the future
    const now = new Date()
    const endDate = new Date(status.end_date)

    return endDate > now
  })
})

// Check if current user can manage maintenance
const canManageTenant = computed(() => {
  return authStore.isAdmin || authStore.isOperator
})

const canManageAll = computed(() => {
  return authStore.isOperator
})

// Get maintenance status for current tenant
const currentStatus = computed((): MaintenanceStatus | null => {
  return maintenanceStore.getMaintenanceStatus(props.tenant?.id)
})

const isActive = computed(() => {
  const status = currentStatus.value
  return status ? isMaintenanceCurrentlyActive(status) : false
})

// Check if maintenance is scheduled but not currently active (future scheduled maintenance)
const isScheduledButNotActive = computed(() => {
  const status = currentStatus.value
  if (!status?.maintenance) return false

  // If it's currently active, it's not "scheduled but not active"
  if (isActive.value) return false

  // If there's no start date, it's indefinite (not scheduled)
  if (!status.start_date) return false

  // Check if start date is in the future
  const now = new Date()
  const start = new Date(status.start_date)

  return start > now
})

// Form state for maintenance activation
// Helper function to get current Japan time for datetime-local input
const toJapanLocalDateTime = (date: Date): string => {
  // Convert to Japan timezone (UTC+9) and format for datetime-local input
  const japanTime = new Date(
    date.toLocaleString('en-US', { timeZone: 'Asia/Tokyo' })
  )

  // Format for datetime-local input (YYYY-MM-DDTHH:mm)
  const year = japanTime.getFullYear()
  const month = String(japanTime.getMonth() + 1).padStart(2, '0')
  const day = String(japanTime.getDate()).padStart(2, '0')
  const hours = String(japanTime.getHours()).padStart(2, '0')
  const minutes = String(japanTime.getMinutes()).padStart(2, '0')

  return `${year}-${month}-${day}T${hours}:${minutes}`
}

const maintenanceForm = reactive({
  message: 'ただいまメンテナンス中です。しばらくしてもう一度お試しください。',
  mode: 'indefinite' as 'indefinite' | 'scheduled', // New: maintenance mode
  start_date: toJapanLocalDateTime(new Date()), // Japan local time for datetime-local input
  end_date: toJapanLocalDateTime(new Date(Date.now() + 60 * 60 * 1000)) // 1 hours from now in Japan time
})

// Auto-refresh interval for maintenance status
let refreshInterval: ReturnType<typeof setInterval> | null = null

// Load maintenance status on mount
onMounted(async () => {
  if (canManageTenant.value) {
    await maintenanceStore.fetchMaintenanceStatus(props.tenant?.id)
  }

  if (canManageAll.value && props.showAllControls) {
    await maintenanceStore.fetchAllActive()
  }

  // Set up auto-refresh every 30 seconds to keep status in sync
  refreshInterval = setInterval(async () => {
    if (canManageTenant.value && props.tenant?.id) {
      await maintenanceStore.fetchMaintenanceStatus(props.tenant.id)
    }
    if (canManageAll.value) {
      await maintenanceStore.fetchAllActive()
    }
  }, 30000) // 30 seconds
})

// Clean up interval on unmount
onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})

// Handle tenant-specific maintenance toggle
const handleToggleTenant = async () => {
  const action = isActive.value ? '無効化' : '有効化'
  const actionDescription = isActive.value
    ? 'メンテナンスモードを無効にします。ユーザーは通常通りチャットボットのサービスを利用できるようになります。'
    : 'メンテナンスモードを有効にします。ユーザーはチャットボットのサービスを利用できなくなります。'

  confirm.show({
    title: `メンテナンス${action}の確認`,
    description: `テナント「${props.tenant?.description}」の${actionDescription}`,
    confirmText: action,
    cancelText: 'キャンセル',
    onConfirm: async () => {
      try {
        const payload: MaintenanceActivationPayload = {
          message: maintenanceForm.message
        }

        // Add dates only for scheduled maintenance
        if (maintenanceForm.mode === 'scheduled') {
          // Send Japan time directly to API (no UTC conversion)
          payload.start_date = formatDateTime(
            new Date(maintenanceForm.start_date)
          )
          payload.end_date = formatDateTime(new Date(maintenanceForm.end_date))
        }

        await maintenanceStore.toggleTenantMaintenance(
          props.tenant?.id,
          payload
        )

        // Show success message
        const toast = useToast()
        toast.add({
          title: 'メンテナンス設定',
          description: `テナント「${props.tenant?.description}」のメンテナンスモードを${action}しました`,
          color: 'green'
        })
      } catch (error) {
        console.error('Failed to toggle maintenance:', error)

        const toast = useToast()
        toast.add({
          title: 'エラー',
          description: 'メンテナンス設定の変更に失敗しました',
          color: 'red'
        })
      }
    }
  })
}

// Handle activate all tenants
const handleActivateAll = async () => {
  confirm.show({
    title: '全テナントメンテナンス有効化の確認',
    description:
      '全てのテナントでメンテナンスモードを有効にします。全ユーザーがサービスを利用できなくなります。この操作を実行しますか？',
    confirmText: '全テナント有効化',
    cancelText: 'キャンセル',
    onConfirm: async () => {
      try {
        const payload: MaintenanceActivationPayload = {
          message: maintenanceForm.message
        }

        // Add dates only for scheduled maintenance
        if (maintenanceForm.mode === 'scheduled') {
          // Send Japan time directly to API (no UTC conversion)
          payload.start_date = formatDateTime(
            new Date(maintenanceForm.start_date)
          )
          payload.end_date = formatDateTime(new Date(maintenanceForm.end_date))
        }

        await maintenanceStore.activateAll(payload)

        const toast = useToast()
        toast.add({
          title: 'メンテナンス設定',
          description: '全テナントのメンテナンスモードを有効にしました',
          color: 'green'
        })
      } catch (error) {
        console.error('Failed to activate all:', error)

        const toast = useToast()
        toast.add({
          title: 'エラー',
          description: '全テナントのメンテナンス有効化に失敗しました',
          color: 'red'
        })
      }
    }
  })
}

// Handle deactivate all tenants
const handleDeactivateAll = async () => {
  confirm.show({
    title: '全テナントメンテナンス無効化の確認',
    description:
      '全てのテナントでメンテナンスモードを無効にします。全ユーザーが通常通りサービスを利用できるようになります。この操作を実行しますか？',
    confirmText: '全テナント無効化',
    cancelText: 'キャンセル',
    onConfirm: async () => {
      try {
        await maintenanceStore.deactivateAll()

        const toast = useToast()
        toast.add({
          title: 'メンテナンス設定',
          description: '全テナントのメンテナンスモードを無効にしました',
          color: 'green'
        })
      } catch (error) {
        console.error('Failed to deactivate all:', error)

        const toast = useToast()
        toast.add({
          title: 'エラー',
          description: '全テナントのメンテナンス無効化に失敗しました',
          color: 'red'
        })
      }
    }
  })
}

// Format date for display
const formatDate = (dateString?: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('ja-JP')
}

// Format date to YYYY-MM-DD HH:mm:ss format for API
const formatDateTime = (date: Date): string => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// Helper function to check if maintenance is currently active
const isMaintenanceCurrentlyActive = (status: MaintenanceStatus): boolean => {
  if (!status.maintenance) return false

  // Indefinite maintenance is always active if maintenance flag is true
  if (!status.start_date || !status.end_date) return true

  // For scheduled maintenance, check if current time is between start and end
  const now = new Date()
  const start = new Date(status.start_date)
  const end = new Date(status.end_date)

  return now >= start && now <= end
}

// Helper function to get appropriate countdown message
const getCountdownMessage = (status: MaintenanceStatus): string => {
  if (!status.end_date) {
    return '継続時間'
  }

  const isCurrentlyActive = isMaintenanceCurrentlyActive(status)
  if (isCurrentlyActive) {
    return '終了まで'
  } else {
    return '開始まで'
  }
}

// Helper function to get status badge color
const getStatusBadgeColor = () => {
  if (!currentStatus.value?.maintenance) return 'green'

  if (isActive.value) {
    return 'red' // Currently in maintenance
  } else {
    if (
      currentStatus.value.end_date
      && new Date(currentStatus.value.end_date) < new Date()
    ) {
      return 'green'
    }
    return 'blue' // Scheduled but not started yet
  }
}

// Helper function to get status badge label
const getStatusBadgeLabel = (): string => {
  if (!currentStatus.value?.maintenance) return '稼働中'
  // if end_date is in the past, consider it expired
  if (
    currentStatus.value.end_date
    && new Date(currentStatus.value.end_date) < new Date()
  ) {
    return '稼働中'
  }

  if (isActive.value) {
    return 'メンテナンス中'
  } else {
    return 'メンテナンス予定'
  }
}

// Helper functions for maintenance status list
const getMaintenanceStatusColor = (status: MaintenanceStatus) => {
  if (!status.end_date) return 'orange' // Indefinite

  const isCurrentlyActive = isMaintenanceCurrentlyActive(status)
  return isCurrentlyActive ? 'red' : 'blue' // Active vs Scheduled
}

const getMaintenanceStatusLabel = (status: MaintenanceStatus): string => {
  if (!status.end_date) return '無期限'

  const isCurrentlyActive = isMaintenanceCurrentlyActive(status)
  return isCurrentlyActive ? '実行中' : '予定'
}

const getMaintenanceStatusDescription = (status: MaintenanceStatus): string => {
  if (!status.end_date) {
    return '無期限メンテナンス'
  }

  const isCurrentlyActive = isMaintenanceCurrentlyActive(status)
  if (isCurrentlyActive) {
    return `終了予定: ${formatDate(status.end_date)}`
  } else {
    return `開始予定: ${formatDate(status.start_date)}`
  }
}

// Helper function to get control section description
const getControlSectionDescription = (): string => {
  if (isScheduledButNotActive.value) {
    return 'スケジュールされたメンテナンスをキャンセルできます'
  } else if (isActive.value) {
    return 'メンテナンスモードを終了できます'
  } else {
    return 'メンテナンスモードの有効/無効を切り替えます'
  }
}

// Helper functions for global maintenance status
const hasActiveGlobalMaintenance = computed(() => {
  return filteredActiveMaintenanceStatus.value.length > 0
})

const hasScheduledGlobalMaintenance = computed(() => {
  return filteredActiveMaintenanceStatus.value.some(status => {
    if (!status.maintenance) return false

    // If there's no start date, it's indefinite (not scheduled)
    if (!status.start_date) return false

    // Check if start date is in the future
    const now = new Date()
    const start = new Date(status.start_date)

    return start > now
  })
})

const hasCurrentlyActiveGlobalMaintenance = computed(() => {
  return filteredActiveMaintenanceStatus.value.some(status => {
    return isMaintenanceCurrentlyActive(status)
  })
})

// Handle cancel scheduled maintenance
const handleCancelScheduledMaintenance = async () => {
  const confirm = useConfirm()

  const action = 'キャンセル'
  const actionDescription
    = 'スケジュールされたメンテナンスをキャンセルします。'

  confirm.show({
    title: `メンテナンス${action}の確認`,
    description: `テナント「${props.tenant?.description}」の${actionDescription}`,
    confirmText: action,
    cancelText: 'キャンセル',
    onConfirm: async () => {
      try {
        await maintenanceStore.deactivateTenant(props.tenant?.id)

        // Show success notification
        const toast = useToast()
        toast.add({
          title: 'メンテナンス予定キャンセル完了',
          description: `テナント「${props.tenant?.description}」のメンテナンス予定をキャンセルしました`,
          color: 'green'
        })
      } catch (error) {
        console.error('Failed to cancel scheduled maintenance:', error)
      }
    }
  })
}

// Handle maintenance expiration or start
const handleMaintenanceExpired = async () => {
  // Refresh maintenance status to get updated state
  if (props.tenant?.id) {
    await maintenanceStore.fetchMaintenanceStatus(props.tenant.id)
  }

  // Check if this is a start or end event
  const status = currentStatus.value
  if (status) {
    const now = new Date()
    const isCurrentlyActive = isMaintenanceCurrentlyActive(status)

    if (isCurrentlyActive && status.start_date) {
      const start = new Date(status.start_date)
      // If we just passed the start time, this is a start event
      if (Math.abs(now.getTime() - start.getTime()) < 60000) {
        // Within 1 minute
        const toast = useToast()
        toast.add({
          title: 'メンテナンス開始',
          description: `テナント「${props.tenant?.description}」のメンテナンスが開始されました`,
          color: 'orange'
        })
        return
      }
    }
  }

  // Default to end notification
  const toast = useToast()
  toast.add({
    title: 'メンテナンス終了',
    description: `テナント「${props.tenant?.description}」のメンテナンス期間が終了しました`,
    color: 'green'
  })
}

// Handle refresh all maintenance status
const handleRefreshAllStatus = async () => {
  try {
    await maintenanceStore.fetchAllActive()

    // Show success notification
    const toast = useToast()
    toast.add({
      title: '状態更新完了',
      description: '全テナントのメンテナンス状態を更新しました',
      color: 'green'
    })
  } catch (error) {
    console.error('Failed to refresh all maintenance status:', error)

    // Show error notification
    const toast = useToast()
    toast.add({
      title: '状態更新エラー',
      description: 'メンテナンス状態の更新に失敗しました',
      color: 'red'
    })
  }
}
</script>

<template>
  <div class="space-y-6">
    <!-- Tenant-specific maintenance control -->
    <UCard v-if="canManageTenant">
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-md font-semibold">
            チャットボットのメンテナンス設定
          </h3>
          <div class="flex items-center gap-3">
            <UBadge
              size="lg"
              :ui="{
                rounded: 'rounded-full'
              }"
              :color="getStatusBadgeColor()"
              :label="getStatusBadgeLabel()"
            />
            <!-- Countdown for maintenance (active or scheduled) -->
            <div
              v-if="isActive || isScheduledButNotActive"
              class="flex flex-col gap-1"
            >
              <MaintenanceCountdown
                v-if="currentStatus"
                :end-date="currentStatus.end_date"
                :start-date="currentStatus.start_date"
                :message="getCountdownMessage(currentStatus)"
                :indefinite="!currentStatus.end_date"
                :is-active="isActive"
                size="sm"
                :variant="isActive ? 'warning' : 'default'"
                @expired="handleMaintenanceExpired"
              />
              <!-- Additional context -->
              <div
                v-if="!currentStatus?.end_date && isActive"
                class="text-xs text-orange-600 font-medium"
              >
                無期限メンテナンス実行中
              </div>
            </div>
          </div>
        </div>
      </template>

      <div class="space-y-6">
        <!-- Scheduled Maintenance Info (when maintenance is scheduled but not active) -->
        <div
          v-if="isScheduledButNotActive"
          class="space-y-4"
        >
          <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-400">
            <div class="flex items-start justify-between mb-3">
              <h5 class="font-medium text-blue-900">
                メンテナンス予定
              </h5>
              <UBadge
                color="blue"
                label="予定"
                size="xs"
              />
            </div>

            <div
              v-if="currentStatus"
              class="text-sm text-blue-700 space-y-2"
            >
              <div
                v-if="currentStatus.message"
                class="bg-white/50 p-2 rounded"
              >
                <span class="font-medium">メッセージ:</span>
                <div class="mt-1 text-gray-800">
                  {{ currentStatus.message }}
                </div>
              </div>

              <p v-if="currentStatus.start_date">
                <span class="font-medium">開始予定:</span>
                {{ formatDate(currentStatus.start_date) }}
              </p>
              <p v-if="currentStatus.end_date">
                <span class="font-medium">終了予定:</span>
                {{ formatDate(currentStatus.end_date) }}
              </p>

              <!-- Countdown to start -->
              <div class="mt-3 p-2 bg-blue-100 rounded">
                <MaintenanceCountdown
                  :end-date="currentStatus.end_date"
                  :start-date="currentStatus.start_date"
                  :message="'開始まで'"
                  :indefinite="false"
                  :is-active="false"
                  size="sm"
                  variant="default"
                  :show-icon="true"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Maintenance Configuration Form -->
        <div
          v-else-if="!isActive && !isScheduledButNotActive"
          class="space-y-6"
        >
          <div>
            <h4 class="font-medium text-gray-900 mb-4">
              メンテナンス設定
            </h4>

            <!-- Maintenance Mode Selection -->
            <UFormGroup
              label="メンテナンスモード"
              description="メンテナンスの種類を選択してください"
              class="mb-4"
            >
              <URadioGroup
                v-model="maintenanceForm.mode"
                :options="[
                  {
                    value: 'indefinite',
                    label: '無期限メンテナンス',
                    description: '手動で終了するまで継続'
                  },
                  {
                    value: 'scheduled',
                    label: 'スケジュールメンテナンス',
                    description: '指定した時間で自動終了'
                  }
                ]"
              >
                <template #label="{ option }">
                  <div class="flex flex-col">
                    <span class="font-medium">{{ option.label }}</span>
                    <span class="text-sm text-gray-500">{{
                      option.description
                    }}</span>
                  </div>
                </template>
              </URadioGroup>
            </UFormGroup>

            <!-- Message Input -->
            <UFormGroup
              label="メンテナンスメッセージ"
              description="ユーザーに表示されるメンテナンスメッセージ"
              class="mb-4"
            >
              <UTextarea
                v-model="maintenanceForm.message"
                placeholder="ただいまメンテナンス中です。しばらくしてもう一度お試しください。"
                :rows="3"
              />
            </UFormGroup>

            <!-- Timezone Information Alert -->
            <UAlert
              v-if="maintenanceForm.mode === 'scheduled'"
              color="blue"
              variant="soft"
              title="タイムゾーンについて"
              description="すべての日時は日本標準時（JST/UTC+9）で入力・表示・処理されます。"
              class="mb-4"
            />

            <!-- Scheduled Maintenance Date Inputs -->
            <div
              v-if="maintenanceForm.mode === 'scheduled'"
              class="grid grid-cols-1 md:grid-cols-2 gap-4"
            >
              <UFormGroup
                label="開始日時"
                description="メンテナンス開始日時（日本時間 JST/UTC+9）"
              >
                <UInput
                  v-model="maintenanceForm.start_date"
                  type="datetime-local"
                />
                <template #help>
                  <div
                    class="flex items-center gap-1 text-xs text-blue-600 mt-1"
                  >
                    <UIcon
                      name="i-heroicons-information-circle"
                      class="w-3 h-3"
                    />
                    <span>日本標準時（JST）で入力してください</span>
                  </div>
                </template>
              </UFormGroup>

              <UFormGroup
                label="終了日時"
                description="メンテナンス終了日時（日本時間 JST/UTC+9）"
              >
                <UInput
                  v-model="maintenanceForm.end_date"
                  type="datetime-local"
                />
                <template #help>
                  <div
                    class="flex items-center gap-1 text-xs text-blue-600 mt-1"
                  >
                    <UIcon
                      name="i-heroicons-information-circle"
                      class="w-3 h-3"
                    />
                    <span>日本標準時（JST）で入力してください</span>
                  </div>
                </template>
              </UFormGroup>
            </div>

            <!-- Indefinite Maintenance Info -->
            <UAlert
              v-if="maintenanceForm.mode === 'indefinite'"
              color="amber"
              variant="soft"
              title="無期限メンテナンス"
              description="このメンテナンスは手動で終了するまで継続されます。終了するには「メンテナンス無効化」ボタンを使用してください。"
              class="mt-4"
            />
          </div>
        </div>

        <!-- Control Section -->
        <div class="space-y-4">
          <div>
            <p class="text-sm text-gray-500">
              {{ getControlSectionDescription() }}
            </p>
          </div>

          <!-- Action Buttons -->
          <div class="flex items-center gap-3">
            <!-- For scheduled but not active maintenance -->
            <UButton
              v-if="isScheduledButNotActive"
              :loading="loadings.deactivateTenant"
              color="orange"
              size="lg"
              icon="i-heroicons-x-mark"
              @click="handleCancelScheduledMaintenance"
            >
              予定キャンセル
            </UButton>

            <!-- For no maintenance or expired maintenance -->
            <UButton
              v-else-if="!isActive && !isScheduledButNotActive"
              :loading="loadings.activateTenant"
              color="red"
              size="lg"
              icon="i-heroicons-exclamation-triangle"
              @click="handleToggleTenant"
            >
              メンテナンス開始
            </UButton>

            <!-- For active maintenance -->
            <UButton
              v-else-if="isActive"
              :loading="loadings.deactivateTenant"
              color="green"
              size="lg"
              icon="i-heroicons-check-circle"
              @click="handleToggleTenant"
            >
              メンテナンス終了
            </UButton>

            <!-- Quick Actions -->
            <template v-if="isActive || isScheduledButNotActive">
              <UButton
                color="gray"
                variant="outline"
                size="lg"
                icon="i-heroicons-arrow-path"
                :loading="loadings.fetchMaintenanceStatus"
                @click="
                  () => maintenanceStore.fetchMaintenanceStatus(tenant?.id)
                "
              >
                状態更新
              </UButton>
            </template>
          </div>
        </div>

        <!-- Status details -->
        <div
          v-if="currentStatus"
          class="space-y-3"
        >
          <!-- Current maintenance info -->
          <div
            v-if="isActive"
            class="bg-red-50 p-4 rounded-lg border-l-4 border-red-400"
          >
            <div class="flex items-start justify-between mb-3">
              <h5 class="font-medium text-red-900">
                現在のメンテナンス情報
              </h5>
              <UBadge
                :color="currentStatus?.end_date ? 'blue' : 'orange'"
                :label="currentStatus?.end_date ? 'スケジュール' : '無期限'"
                size="xs"
              />
            </div>

            <div class="text-sm text-red-700 space-y-2">
              <div
                v-if="currentStatus.message"
                class="bg-white/50 p-2 rounded"
              >
                <span class="font-medium">メッセージ:</span>
                <div class="mt-1 text-gray-800">
                  {{ currentStatus.message }}
                </div>
              </div>

              <!-- Scheduled maintenance info -->
              <template v-if="currentStatus.end_date">
                <p v-if="currentStatus.start_date">
                  <span class="font-medium">開始日時:</span>
                  {{ formatDate(currentStatus.start_date) }}
                </p>
                <p>
                  <span class="font-medium">終了予定:</span>
                  {{ formatDate(currentStatus.end_date) }}
                </p>
              </template>

              <!-- Indefinite maintenance info -->
              <template v-else>
                <div class="bg-orange-50 p-2 rounded border border-orange-200">
                  <div class="flex items-center gap-2 text-orange-800">
                    <UIcon
                      name="i-heroicons-exclamation-triangle"
                      class="w-4 h-4"
                    />
                    <span class="font-medium">無期限メンテナンス</span>
                  </div>
                  <p class="text-xs text-orange-700 mt-1">
                    このメンテナンスは手動で終了するまで継続されます
                  </p>
                  <p
                    v-if="currentStatus.start_date"
                    class="text-xs text-orange-600 mt-1"
                  >
                    開始: {{ formatDate(currentStatus.start_date) }}
                  </p>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Operator-only controls -->
    <UCard v-if="canManageAll && showAllControls">
      <template #header>
        <h3 class="text-lg font-semibold">
          全テナント制御 (オペレーター専用)
        </h3>
      </template>

      <div class="space-y-6">
        <!-- Global Maintenance Status Display (when there are active/scheduled maintenances) -->
        <div
          v-if="hasScheduledGlobalMaintenance && !hasCurrentlyActiveGlobalMaintenance"
          class="space-y-4"
        >
          <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-400">
            <div class="flex items-start justify-between mb-3">
              <h5 class="font-medium text-blue-900">
                全テナントメンテナンス予定
              </h5>
              <UBadge
                color="blue"
                label="予定"
                size="xs"
              />
            </div>
            <div class="text-sm text-blue-700">
              <p>一部のテナントでメンテナンスが予定されています。</p>
            </div>
          </div>
        </div>

        <div
          v-else-if="hasCurrentlyActiveGlobalMaintenance"
          class="space-y-4"
        >
          <div class="bg-red-50 p-4 rounded-lg border-l-4 border-red-400">
            <div class="flex items-start justify-between mb-3">
              <h5 class="font-medium text-red-900">
                全テナントメンテナンス実行中
              </h5>
              <UBadge
                color="red"
                label="実行中"
                size="xs"
              />
            </div>
            <div class="text-sm text-red-700">
              <p>一部のテナントでメンテナンスが実行中です。</p>
            </div>
          </div>
        </div>

        <!-- Global Maintenance Configuration Form (only show when no active/scheduled maintenances) -->
        <div
          v-else-if="!hasActiveGlobalMaintenance"
          class="space-y-6"
        >
          <div>
            <h4 class="font-medium text-gray-900 mb-4">
              全テナント用メンテナンス設定
            </h4>

            <!-- Global Maintenance Mode Selection -->
            <UFormGroup
              label="メンテナンスモード"
              description="全テナント用メンテナンスの種類を選択してください"
              class="mb-4"
            >
              <URadioGroup
                v-model="maintenanceForm.mode"
                :options="[
                  {
                    value: 'indefinite',
                    label: '無期限メンテナンス',
                    description: '手動で終了するまで継続'
                  },
                  {
                    value: 'scheduled',
                    label: 'スケジュールメンテナンス',
                    description: '指定した時間で自動終了'
                  }
                ]"
              >
                <template #label="{ option }">
                  <div class="flex flex-col">
                    <span class="font-medium">{{ option.label }}</span>
                    <span class="text-sm text-gray-500">{{
                      option.description
                    }}</span>
                  </div>
                </template>
              </URadioGroup>
            </UFormGroup>

            <!-- Global Message Input -->
            <UFormGroup
              label="メンテナンスメッセージ"
              description="全テナントに適用されるメンテナンスメッセージ"
              class="mb-4"
            >
              <UTextarea
                v-model="maintenanceForm.message"
                placeholder="ただいまメンテナンス中です。しばらくしてもう一度お試しください。"
                :rows="3"
              />
            </UFormGroup>

            <!-- Global Timezone Information Alert -->
            <UAlert
              v-if="maintenanceForm.mode === 'scheduled'"
              color="blue"
              variant="soft"
              title="タイムゾーンについて"
              description="すべての日時は日本標準時（JST/UTC+9）で入力・表示・処理されます。"
              class="mb-4"
            />

            <!-- Global Scheduled Maintenance Date Inputs -->
            <div
              v-if="maintenanceForm.mode === 'scheduled'"
              class="grid grid-cols-1 md:grid-cols-2 gap-4"
            >
              <UFormGroup
                label="開始日時"
                description="全テナント用メンテナンス開始日時（日本時間 JST/UTC+9）"
              >
                <UInput
                  v-model="maintenanceForm.start_date"
                  type="datetime-local"
                />
                <template #help>
                  <div
                    class="flex items-center gap-1 text-xs text-blue-600 mt-1"
                  >
                    <UIcon
                      name="i-heroicons-information-circle"
                      class="w-3 h-3"
                    />
                    <span>日本標準時（JST）で入力してください</span>
                  </div>
                </template>
              </UFormGroup>

              <UFormGroup
                label="終了日時"
                description="全テナント用メンテナンス終了日時（日本時間 JST/UTC+9）"
              >
                <UInput
                  v-model="maintenanceForm.end_date"
                  type="datetime-local"
                />
                <template #help>
                  <div
                    class="flex items-center gap-1 text-xs text-blue-600 mt-1"
                  >
                    <UIcon
                      name="i-heroicons-information-circle"
                      class="w-3 h-3"
                    />
                    <span>日本標準時（JST）で入力してください</span>
                  </div>
                </template>
              </UFormGroup>
            </div>

            <!-- Global Indefinite Maintenance Warning -->
            <UAlert
              v-if="maintenanceForm.mode === 'indefinite'"
              color="red"
              variant="soft"
              title="⚠️ 全テナント無期限メンテナンス"
              description="この操作により全てのテナントが無期限でメンテナンスモードになります。終了するには「全テナント無効化」ボタンを使用してください。"
              class="mt-4"
            />
          </div>
        </div>

        <!-- Control Section -->
        <div class="space-y-4">
          <div>
            <p class="font-medium">
              全テナント一括制御
            </p>
            <p class="text-sm text-gray-500">
              {{ hasActiveGlobalMaintenance ? 'アクティブなメンテナンスを管理します' : '全テナントのメンテナンスモードを一括で制御します' }}
            </p>
          </div>

          <div class="flex gap-2 flex-wrap">
            <!-- Show activate button only when no active maintenance -->
            <UButton
              v-if="!hasActiveGlobalMaintenance"
              :loading="loadings.activateAll"
              color="red"
              size="lg"
              icon="i-heroicons-exclamation-triangle"
              @click="handleActivateAll"
            >
              全テナント有効化
            </UButton>

            <!-- Show deactivate button when there are active maintenances -->
            <UButton
              v-if="hasActiveGlobalMaintenance"
              :loading="loadings.deactivateAll"
              color="green"
              size="lg"
              icon="i-heroicons-check-circle"
              @click="handleDeactivateAll"
            >
              全テナント無効化
            </UButton>

            <UButton
              color="gray"
              variant="outline"
              icon="i-heroicons-arrow-path"
              :loading="loadings.fetchAllActive"
              @click="handleRefreshAllStatus"
            >
              状態更新
            </UButton>
          </div>
        </div>

        <!-- Active maintenance status -->
        <div v-if="filteredActiveMaintenanceStatus.length > 0">
          <p class="font-medium mb-2">
            メンテナンス中・予定のテナント ({{
              filteredActiveMaintenanceStatus.length
            }}件)
          </p>
          <div class="space-y-2">
            <div
              v-for="status in filteredActiveMaintenanceStatus"
              :key="status.tenant_id"
              class="p-3 bg-red-50 rounded-lg border-l-4"
              :class="
                getMaintenanceStatusColor(status) === 'red'
                  ? 'border-red-400'
                  : getMaintenanceStatusColor(status) === 'blue'
                    ? 'border-blue-400'
                    : 'border-orange-400'
              "
            >
              <div class="flex items-start justify-between">
                <div class="flex flex-col gap-1 flex-1">
                  <div class="flex items-center gap-2">
                    <span class="font-mono font-semibold">{{
                      status.tenant_id
                    }}</span>
                    <UBadge
                      :color="getMaintenanceStatusColor(status)"
                      :label="getMaintenanceStatusLabel(status)"
                      size="xs"
                    />
                  </div>
                  <span class="text-xs text-gray-600">
                    開始:
                    {{
                      status.start_date ? formatDate(status.start_date) : "不明"
                    }}
                  </span>

                  <!-- Indefinite maintenance warning -->
                  <div
                    v-if="!status.end_date"
                    class="flex items-center gap-1 text-xs text-orange-600 mt-1"
                  >
                    <UIcon
                      name="i-heroicons-exclamation-triangle"
                      class="w-3 h-3"
                    />
                    <span>手動終了まで継続</span>
                  </div>
                </div>

                <div class="flex flex-col items-end gap-1">
                  <MaintenanceCountdown
                    :end-date="status.end_date"
                    :start-date="status.start_date"
                    :message="getCountdownMessage(status)"
                    :indefinite="!status.end_date"
                    :is-active="isMaintenanceCurrentlyActive(status)"
                    size="sm"
                    variant="danger"
                    :show-icon="false"
                  />
                  <span class="text-xs text-gray-500">
                    {{ getMaintenanceStatusDescription(status) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else>
          <p class="text-sm text-gray-500">
            現在メンテナンス中・予定のテナントはありません
          </p>
        </div>
      </div>
    </UCard>
  </div>
</template>
