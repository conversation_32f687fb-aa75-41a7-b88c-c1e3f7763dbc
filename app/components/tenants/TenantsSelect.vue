<script setup lang="ts">
const authStore = useAuthStore()
const tenantsStore = useTenantsStore()
const environmentsStore = useEnvironmentsStore()
const {
  loadings,
  selectedTenant,
  selectedTenantId,
  allTenantsDropdownItems,
  showTenantsPalette
} = storeToRefs(tenantsStore)
const { isOperator } = storeToRefs(authStore)

// Command palette groups
const commandPaletteGroups = computed(() => [
  {
    key: 'tenants',
    label: 'テナント一覧',
    commands: [...allTenantsDropdownItems.value]
  }
])

// Handle tenant selection from command palette
const handleTenantSelect = (tenant: any) => {
  // if click to current tenant, do nothing
  if (tenant?.id === selectedTenantId.value) {
    return
  }
  if (tenant?.click) {
    tenant.click()
  }
}

// Open tenant selection modal
const openTenantsPalette = () => {
  tenantsStore.showTenantsPalette = true
  // Fetch all tenants when opening the palette
  tenantsStore.fetchAllTenants()
}

onMounted(() => {
  if (selectedTenantId.value) {
    environmentsStore.fetchAllEnvs(selectedTenantId.value)
  }
})
</script>

<template>
  <!-- Tenant Selection Button for Operators -->
  <UButton
    v-if="isOperator"
    color="gray"
    variant="ghost"
    class="w-full justify-start"
    block
    :loading="loadings.fetchTenants"
    @click="openTenantsPalette"
  >
    <UAvatar
      :src="selectedTenant?.avatar?.src"
      :alt="selectedTenant?.label?.toUpperCase()"
      size="2xs"
      :ui="{
        rounded: 'rounded-sm'
      }"
    />

    <div
      class="flex flex-row gap-1 items-center justify-between w-full truncate"
    >
      <span class="text-gray-900 dark:text-white font-semibold truncate">{{
        selectedTenant?.label
      }}</span>
    </div>
    <UIcon
      name="octicon:arrow-switch-24"
      class="ml-auto text-lg"
    />
  </UButton>

  <!-- Tenant Display for Non-Operators -->
  <div
    v-else
    class="flex flex-row gap-1 items-center justify-between w-full truncate px-4"
  >
    <UAvatar
      :src="selectedTenant?.avatar?.src"
      :alt="selectedTenant?.label?.toUpperCase()"
      size="2xs"
      :ui="{
        rounded: 'rounded-sm'
      }"
    />

    <div
      class="flex flex-row gap-1 items-center justify-between w-full truncate"
    >
      <span class="text-gray-900 dark:text-white font-semibold truncate">{{
        selectedTenant?.label
      }}</span>
    </div>
  </div>

  <!-- Tenant Selection Modal with Command Palette -->
  <UModal
    v-model="showTenantsPalette"
    :ui="{ height: 'max-h-[80vh]' }"
  >
    <UCommandPalette
      :groups="commandPaletteGroups"
      :loading="loadings.fetchAllTenants"
      placeholder="テナントを検索..."
      :autoselect="false"
      :fuse="{
        fuseOptions: {
          ignoreLocation: true,
          includeMatches: true,
          threshold: 0,
          keys: ['id', 'description']
        },
        resultLimit: 100
      }"
      :ui="{
        group: {
          command: {
            base: 'group'
          }
        }
      }"
      @update:model-value="handleTenantSelect"
    >
      <!-- Custom command display for tenants -->
      <template #tenants-command="{ command }">
        <div class="flex items-center gap-3 w-full">
          <!-- Tenant Icon/Avatar -->
          <div class="flex-shrink-0">
            <UAvatar
              :src="command?.avatar?.src"
              :alt="command?.label?.toUpperCase()"
              size="md"
              :ui="{
                rounded: 'rounded-md',
                background:
                  command?.id === selectedTenantId
                    ? 'bg-primary-200 dark:bg-primary-900'
                    : 'bg-gray-200 dark:bg-gray-700'
              }"
              :class="{
                'ring-1 ring-primary': command?.id === selectedTenantId
              }"
            />
          </div>

          <!-- Tenant Info -->
          <div class="flex-1 min-w-0">
            <div class="flex items-center gap-2">
              <span
                class="truncate"
                :class="{
                  'text-primary-500 dark:text-primary-500 font-bold':
                    command?.id === selectedTenantId,
                  'text-gray-500 dark:text-gray-400 font-medium ':
                    command?.id !== selectedTenantId
                }"
              >
                {{ command.description || command.id }}
              </span>
            </div>
            <div
              :class="{
                'text-primary-400 dark:text-primary-600':
                  command?.id === selectedTenantId,
                'text-gray-500 dark:text-gray-400':
                  command?.id !== selectedTenantId
              }"
              class="text-sm truncate"
            >
              {{ command.id }}
            </div>
          </div>
        </div>
      </template>

      <template #tenants-inactive="{ command }">
        <div
          v-if="command?.id === selectedTenantId"
          class="text-sm text-primary-400 dark:text-primary-600"
        >
          選択中
        </div>
        <div
          v-else
          class="group-hover:block hidden text-xs text-gray-500 dark:text-gray-400"
        >
          クリックして切り替え
        </div>
      </template>
    </UCommandPalette>
  </UModal>
</template>
