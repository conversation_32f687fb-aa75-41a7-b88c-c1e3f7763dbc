# Maintenance Store Usage Guide

このドキュメントでは、新しく作成されたメンテナンスストア (`useMaintenanceStore`) の使用方法について説明します。

## 概要

メンテナンスストアは、テナントレベルのメンテナンスモードを管理するためのストアです。以下のAPIエンドポイントに対応しています：

- **Activate Tenant**: 特定テナントのメンテナンスモード有効化 (Admin以上)
- **Deactivate Tenant**: 特定テナントのメンテナンスモード無効化 (Admin以上)
- **Activate All**: 全テナントのメンテナンスモード有効化 (Operator専用)
- **Deactivate All**: 全テナントのメンテナンスモード無効化 (Operator専用)
- **Get Status**: 特定テナントのメンテナンス状態取得 (Staff以上)
- **Get All Active**: 全アクティブメンテナンス状態取得 (Operator専用)

## 型定義

```typescript
// app/types/index.d.ts に追加された型
export interface MaintenanceActivationPayload {
  message: string
  start_date?: string // Format: YYYY-MM-DD HH:mm:ss (optional for indefinite maintenance)
  end_date?: string   // Format: YYYY-MM-DD HH:mm:ss (optional for indefinite maintenance)
}

export interface MaintenanceStatus {
  message: string
  maintenance: boolean
  start_date?: string // Optional for indefinite maintenance
  end_date?: string   // Optional for indefinite maintenance
  tenant_id: string
}

export interface AllActiveMaintenanceResponse {
  tenants: MaintenanceStatus[]
}
```

## メンテナンスモード

### 1. スケジュールメンテナンス
開始日時と終了日時を指定するメンテナンスモードです。指定した時間になると自動的に終了します。

```typescript
const scheduledPayload = {
  message: 'システムメンテナンス中です',
  start_date: '2024-12-31 23:00:00',
  end_date: '2024-12-31 23:59:59'
}
```

### 2. 無期限メンテナンス
開始日時と終了日時を指定しないメンテナンスモードです。手動で終了するまで継続されます。

```typescript
const indefinitePayload = {
  message: 'システムメンテナンス中です'
  // start_date と end_date は省略
}
```

## ストアの使用方法

### 基本的な使用例

```vue
<script setup lang="ts">
const maintenanceStore = useMaintenanceStore()
const { selectedTenantId } = useApp()

// ストアの状態を取得
const { loadings, errors } = storeToRefs(maintenanceStore)

// 特定テナントのメンテナンス状態を取得
const currentStatus = computed(() => {
  return maintenanceStore.getMaintenanceStatus(selectedTenantId.value)
})

// メンテナンスが有効かどうかを確認
const isActive = computed(() => {
  return maintenanceStore.isMaintenanceActive(selectedTenantId.value)
})

// コンポーネントマウント時にメンテナンス状態を取得
onMounted(async () => {
  await maintenanceStore.fetchMaintenanceStatus(selectedTenantId.value)
})
</script>
```

### 利用可能なアクション

#### 1. 特定テナントのメンテナンス制御

```typescript
// メンテナンスモード有効化（ペイロード必須）
const formatDateTime = (date: Date): string => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

const payload = {
  message: 'メンテナンスモード',
  start_date: formatDateTime(new Date()),
  end_date: formatDateTime(new Date(Date.now() + 24 * 60 * 60 * 1000)) // 24時間後
}
await maintenanceStore.activateTenant(tenantId, payload)

// メンテナンスモード無効化
await maintenanceStore.deactivateTenant(tenantId)

// トグル（現在の状態に応じて有効/無効を切り替え）
// 有効化時のペイロードは省略可能（デフォルト値が使用される）
await maintenanceStore.toggleTenantMaintenance(tenantId)

// または、カスタムペイロードを指定
await maintenanceStore.toggleTenantMaintenance(tenantId, payload)

// 無期限メンテナンス（日時指定なし）
const indefinitePayload = {
  message: 'システムメンテナンス中です'
  // start_date と end_date を省略
}
await maintenanceStore.activateTenant(tenantId, indefinitePayload)
```

#### 2. 全テナントの制御（Operator専用）

```typescript
// 全テナントのメンテナンスモード有効化（ペイロード必須）
const payload = {
  message: 'システム全体メンテナンス',
  start_date: formatDateTime(new Date()),
  end_date: formatDateTime(new Date(Date.now() + 24 * 60 * 60 * 1000)) // 24時間後
}
await maintenanceStore.activateAll(payload)

// 全テナントのメンテナンスモード無効化
await maintenanceStore.deactivateAll()
```

#### 3. 状態の取得

```typescript
// 特定テナントの状態取得
await maintenanceStore.fetchMaintenanceStatus(tenantId)

// 全アクティブメンテナンス状態取得（Operator専用）
await maintenanceStore.fetchAllActive()
```

### ゲッター

```typescript
// 特定テナントのメンテナンス状態を取得
const status = maintenanceStore.getMaintenanceStatus(tenantId)

// 特定テナントがメンテナンス中かどうかを確認
const isActive = maintenanceStore.isMaintenanceActive(tenantId)

// アクティブなメンテナンステナント数を取得
const activeCount = maintenanceStore.activeMaintenanceCount
```

## コンポーネントの使用例

作成された `MaintenanceControl` コンポーネントを使用する例：

```vue
<template>
  <div>
    <!-- 基本的な使用 -->
    <MaintenanceControl :tenant-id="selectedTenantId" />
    
    <!-- Operator用の全制御機能を含む -->
    <MaintenanceControl 
      :tenant-id="selectedTenantId" 
      :show-all-controls="true" 
    />
  </div>
</template>

<script setup lang="ts">
const { selectedTenantId } = useApp()
</script>
```

## 権限制御

メンテナンスストアは、ユーザーの権限に基づいて適切なAPIエンドポイントを呼び出します：

- **Staff**: 自分のテナントの状態取得のみ
- **Admin**: 自分のテナントの制御（有効化/無効化/状態取得）
- **Operator**: 全テナントの制御（個別制御 + 一括制御 + 全状態取得）

## エラーハンドリング

```typescript
try {
  await maintenanceStore.activateTenant(tenantId)
} catch (error) {
  // エラーはストアの errors 状態にも保存される
  console.error('Maintenance activation failed:', error)
  console.log('Store errors:', maintenanceStore.errors)
}

// エラーをクリア
maintenanceStore.clearErrors()
maintenanceStore.clearError('activateTenant')
```

## API仕様

### Activate Tenant API

**リクエスト**:
```json
PUT /v2/maintenance/activate/tenants/{tenant_id}
{
  "message": "メンテナンスモード",
  "start_date": "2019-08-24 14:15:22",
  "end_date": "2019-08-24 14:15:22"
}
```

**レスポンス**:
```json
{
  "message": "メンテナンスモード",
  "maintenance": true,
  "start_date": "2019-08-24 14:15:22",
  "end_date": "2019-08-24 14:15:22",
  "tenant_id": "string"
}
```

### Activate All API

**リクエスト**:
```json
PUT /v2/maintenance/activate/all
{
  "message": "システム全体メンテナンス",
  "start_date": "2019-08-24 14:15:22",
  "end_date": "2019-08-24 14:15:22"
}
```

### Get All Active API

**レスポンス**:
```json
GET /v2/maintenance/active
{
  "tenants": [
    {
      "message": "メンテナンスモード",
      "maintenance": true,
      "start_date": "2019-08-24 14:15:22",
      "end_date": "2019-08-24 14:15:22",
      "tenant_id": "string"
    }
  ]
}
```

### その他のAPI

- **Deactivate Tenant**: `PUT /v2/maintenance/deactivate/tenants/{tenant_id}` (ボディなし)
- **Deactivate All**: `PUT /v2/maintenance/deactivate/all` (ボディなし)
- **Get Status**: `GET /v2/maintenance/tenants/{tenant_id}` - MaintenanceStatusオブジェクトを返す

## 注意事項

1. **権限チェック**: APIレベルで権限チェックが行われるため、権限のないユーザーが操作を試行すると403エラーが返されます。

2. **状態の同期**: ローカル状態は楽観的に更新されますが、APIエラーが発生した場合は適切にエラーハンドリングを行ってください。

3. **リアルタイム更新**: 他のユーザーによる変更はリアルタイムで反映されません。必要に応じて定期的に状態を再取得してください。

4. **日時形式**: `start_date`と`end_date`は`YYYY-MM-DD HH:mm:ss`形式の文字列で指定してください。

5. **メッセージ**: `message`フィールドはユーザーに表示されるメンテナンスメッセージです。

6. **日時フォーマット関数**: 正しい日時形式を生成するために、提供されている`formatDateTime`関数を使用してください。

## 既存のメンテナンス設定との違い

- **既存**: 環境レベルのチャットボット/管理画面メンテナンス設定（`/settings/maintenance`）
- **新規**: テナントレベルのメンテナンスモード制御（このストア）

これらは異なる目的で使用され、併用することができます。
