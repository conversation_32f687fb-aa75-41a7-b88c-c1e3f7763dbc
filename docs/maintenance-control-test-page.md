# Maintenance Control Test Page

## 概要

メンテナンス制御機能をテストするための専用ページを作成しました。このページでは `MaintenanceControl` コンポーネントの動作を確認し、メンテナンスストアのAPIを直接テストできます。

## アクセス方法

### URL
```
/{tenantId}/{env}/settings/maintenance-control
```

### ナビゲーション
1. 管理画面にログイン
2. 左サイドバーの「設定」セクションを開く
3. 「メンテナンス制御テスト」をクリック

## ページ機能

### 1. ユーザー情報表示
- 現在のユーザー名
- ユーザーロール（Staff/Admin/Operator）
- 選択中のテナントID

### 2. 権限サマリー
- メンテナンス状態表示権限
- テナント制御権限  
- 全テナント制御権限

### 3. MaintenanceControl コンポーネントテスト

#### 基本制御（Admin以上）
- 特定テナントのメンテナンス有効化/無効化
- メンテナンス状態の表示
- 操作履歴の表示

#### 高度な制御（Operator専用）
- 全テナントの一括有効化/無効化
- アクティブメンテナンステナント一覧
- 全テナント制御機能

### 4. API直接テスト
- **状態取得テスト**: `fetchMaintenanceStatus()` を直接実行
- **全状態取得テスト**: `fetchAllActive()` を直接実行（Operator専用）
- **エラークリア**: ストアのエラー状態をクリア

### 5. ストア状態表示
リアルタイムでメンテナンスストアの状態を表示：
- `maintenanceStatus`: 各テナントのメンテナンス状態
- `allActiveMaintenanceStatus`: 全アクティブメンテナンス一覧
- `loadings`: ローディング状態
- `errors`: エラー状態

## テスト手順

### 1. 基本機能テスト（Admin権限）
1. ページにアクセス
2. 「基本制御」セクションでメンテナンス有効化ボタンをクリック
3. 状態が「メンテナンス中」に変わることを確認
4. 無効化ボタンをクリックして「稼働中」に戻ることを確認
5. ストア状態表示で `maintenanceStatus` が更新されることを確認

### 2. API直接テスト
1. 「状態取得テスト」ボタンをクリック
2. 成功トーストが表示されることを確認
3. ストア状態表示でデータが更新されることを確認

### 3. Operator専用機能テスト（Operator権限）
1. 「高度な制御」セクションが表示されることを確認
2. 「全テナント有効化」ボタンをテスト
3. 「全状態取得テスト」ボタンをクリック
4. `allActiveMaintenanceStatus` にデータが表示されることを確認

### 4. 権限制御テスト
1. Staff権限でアクセス → 基本表示のみ、制御ボタンは無効
2. Admin権限でアクセス → テナント制御可能、全テナント制御は非表示
3. Operator権限でアクセス → 全機能利用可能

## エラーテスト

### 1. 権限エラー
- Staff権限で制御操作を試行 → 403エラーが発生することを確認
- エラー状態がストアに保存されることを確認

### 2. ネットワークエラー
- ネットワークを切断してAPI操作を実行
- エラートーストが表示されることを確認
- ストアのエラー状態が更新されることを確認

### 3. エラー回復
- 「エラークリア」ボタンでエラー状態がクリアされることを確認

## 注意事項

1. **本番環境での使用**: このページは開発・テスト用です。本番環境では適切な権限制御を確認してください。

2. **API制限**: 実際のAPIエンドポイントが実装されていない場合、エラーが発生する可能性があります。

3. **権限確認**: 各機能は実際のユーザー権限に基づいて制御されます。

4. **データ永続化**: ページリロード時にストア状態がリセットされる場合があります。

## トラブルシューティング

### よくある問題

1. **ページが表示されない**
   - ユーザーがログインしているか確認
   - 適切な権限があるか確認

2. **ボタンが無効になっている**
   - ユーザーロールを確認
   - ネットワーク接続を確認

3. **エラーが継続する**
   - 「エラークリア」ボタンを使用
   - ページをリロード
   - ブラウザの開発者ツールでコンソールエラーを確認

### デバッグ情報

ページ下部の「ストア状態表示」セクションで以下の情報を確認できます：
- API呼び出しの結果
- ローディング状態
- エラーの詳細
- ストアの内部状態

この情報を使用して問題を特定し、デバッグを行ってください。
